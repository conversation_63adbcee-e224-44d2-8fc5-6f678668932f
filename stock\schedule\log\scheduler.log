2025-08-14 15:04:25,854 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:04:25,854 - INFO - 🚀 立即执行任务: env_update
2025-08-14 15:04:25,855 - INFO - 🚀 开始执行任务: env_update - 更新stock_env环境的所有pip包
2025-08-14 15:04:25,855 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:04:25,855 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:04:27,704 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:04:27,704 - INFO - 🔄 重试执行任务 env_update (第1次重试)
2025-08-14 15:05:01,544 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:05:01,544 - INFO - 🛑 调度器已停止
2025-08-14 15:05:12,746 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:05:12,746 - INFO - 🛑 调度器已停止
2025-08-14 15:06:12,091 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:06:12,092 - INFO - 🛑 调度器已停止
2025-08-14 15:06:23,795 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:06:23,796 - INFO - 🛑 调度器已停止
2025-08-14 15:07:13,159 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:07:13,160 - INFO - 🛑 调度器已停止
2025-08-14 15:07:26,466 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:07:26,468 - INFO - 🛑 调度器已停止
2025-08-14 15:08:14,399 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:14,399 - INFO - 🛑 调度器已停止
2025-08-14 15:08:24,689 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:24,689 - INFO - 🛑 调度器已停止
2025-08-14 15:08:55,893 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:08:55,895 - INFO - 🛑 调度器已停止
2025-08-14 15:11:27,899 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:11:27,899 - INFO - 🛑 调度器已停止
2025-08-14 15:11:57,802 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:11:57,803 - INFO - 🛑 调度器已停止
2025-08-14 15:14:27,706 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 15:14:27,706 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 15:14:29,446 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 15:14:29,446 - INFO - 🔄 重试执行任务 env_update (第2次重试)
2025-08-14 15:16:28,424 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:16:28,424 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:16:28,425 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:16:28,425 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py today
2025-08-14 15:16:28,425 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:16:29,821 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 66, in main
    print("\U0001f680 ͣȷѯԶű")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f680' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 131, in <module>
    success = main()
              ^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 124, in main
    print(f"\u274c ִʧ: {str(e)}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-08-14 15:16:29,821 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 15:16:29,821 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 15:18:17,367 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:18:17,367 - INFO - 🛑 调度器已停止
2025-08-14 15:18:30,006 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:18:30,007 - INFO - 🛑 调度器已停止
2025-08-14 15:20:19,418 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:20:19,420 - INFO - 🛑 调度器已停止
2025-08-14 15:21:47,559 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:21:47,559 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:21:47,559 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:21:47,560 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py today
2025-08-14 15:21:47,560 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:21:49,110 - INFO - [precise_query] 输出: ͣȷѯԶű
==================================================
ڻȡ...
ѯ: 2025-08-14
ִʧ: 'gbk' codec can't encode character '\U0001f4c1' in position 0: illegal multibyte sequence
2025-08-14 15:21:49,110 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 104, in main
    file_path = get_default_file_path()
                ^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 832, in get_default_file_path
    print(f"\U0001f4c1 ĬExcelļ: {default_file_path}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c1' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 131, in <module>
    success = main()
              ^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery_auto.py", line 126, in main
    print(f"ϸ: {traceback.format_exc()}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c1' in position 332: illegal multibyte sequence
2025-08-14 15:21:49,111 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 15:21:49,111 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 15:23:19,071 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 15:23:19,071 - INFO - 🛑 调度器已停止
2025-08-14 15:26:17,198 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 15:26:17,199 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 15:26:17,199 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 15:26:17,199 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\run_precise_query.py
2025-08-14 15:26:17,199 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 15:26:21,510 - INFO - [precise_query] 输出: ͣȷѯ...
 
 (1/2): ...
  1 
  1 
 20250814 ...
 42 
:
: ['', '', '', '', '', '[20250814]', '[20250814]', '', '[20250814]', '[20250814]', 'a()[20250814]', '[20250814]', '[20250814]', 'a[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', '[20250814]', 'dde', '', '[20250814]', 'market_code', 'code']
...
 42 
 27 
  15 
 42  15 
:
 20250814 
...
 sheet '0814'  42 
 20250814 
 
 : 1 
  1 
ͣȷѯִгɹ
2025-08-14 15:26:21,511 - INFO - ✅ 任务 precise_query 执行成功
2025-08-14 16:22:45,864 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 16:22:45,864 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 16:22:45,865 - INFO - ⏭️ 任务 precise_query 今天已完成，跳过执行
2025-08-14 16:25:54,418 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 16:25:54,419 - INFO - 🚀 立即执行任务: precise_query
2025-08-14 16:25:54,419 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 16:25:54,419 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py --single
2025-08-14 16:25:54,419 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 16:25:56,188 - INFO - [precise_query] 输出: ڻȡ...
2025-08-14 16:25:56,188 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1043, in main
    query_dates = parse_dates_from_args(args)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 849, in parse_dates_from_args
    print(f"\U0001f4c5 ʹõ: {query_date.strftime('%Y%m%d')}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c5' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1123, in <module>
    main()
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1106, in main
    print(f"\u274c ִʧ: {str(e)}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-08-14 16:25:56,189 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 16:25:56,189 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 16:26:16,897 - INFO - 收到信号 2，正在停止调度器...
2025-08-14 16:26:16,897 - INFO - 🛑 调度器已停止
2025-08-14 16:27:39,211 - INFO - 📅 股票数据定时任务调度器已初始化
2025-08-14 16:27:39,212 - INFO - 🚀 启动股票数据定时任务调度器...
2025-08-14 16:27:39,212 - INFO - ⏰ 开机启动任务将在 1 分钟后执行
2025-08-14 16:27:39,212 - INFO - 📅 已调度每日任务: 15:05 - precise_query
2025-08-14 16:27:39,212 - INFO - 📅 已调度每日任务: 15:10 - fupan_data
2025-08-14 16:27:39,213 - INFO - 📅 已调度每日任务: 16:00 - kline_data
2025-08-14 16:27:39,213 - INFO - 📅 已调度每日任务: 16:30 - stock_select
2025-08-14 16:27:39,213 - INFO - 📅 已调度每日任务: 17:10 - thsfp_main
2025-08-14 16:27:39,213 - INFO - ⚡ 检测到错过的任务，立即执行: precise_query
2025-08-14 16:27:39,213 - INFO - 🚀 开始执行任务: precise_query - 获取当日涨停精确查询数据
2025-08-14 16:27:39,213 - INFO - ⚡ 检测到错过的任务，立即执行: fupan_data
2025-08-14 16:27:39,215 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py --single
2025-08-14 16:27:39,215 - INFO - 🚀 开始执行任务: fupan_data - 获取当天复盘数据（涨停精确查询和市场分析）
2025-08-14 16:27:39,215 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 16:27:39,215 - INFO - ⚡ 检测到错过的任务，立即执行: kline_data
2025-08-14 16:27:39,215 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Fupan\daily_limit-marketanalysis.py 2025-08-14 true
2025-08-14 16:27:39,216 - INFO - 📋 当前调度任务:
2025-08-14 16:27:39,216 - INFO - 工作目录: d:\Documents\Code\stock\Fupan
2025-08-14 16:27:39,216 - INFO - 🚀 开始执行任务: kline_data - 获取股票K线数据
2025-08-14 16:27:39,216 - INFO -   ⏰ startup+1min: env_update - 更新stock_env环境的所有pip包
2025-08-14 16:27:39,216 - INFO -   ⏰ 15:05: precise_query - 获取当日涨停精确查询数据
2025-08-14 16:27:39,216 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Zge\fetch_kline.py --datasource tushare --frequency 4 --exclude-gem --min-mktcap 5e9 --max-mktcap +inf --start 20190101 --end today --out stock/Zge/data --workers 8
2025-08-14 16:27:39,216 - INFO -   ⏰ 15:10: fupan_data - 获取当天复盘数据（涨停精确查询和市场分析）
2025-08-14 16:27:39,216 - INFO - 工作目录: d:\Documents\Code\stock\Zge
2025-08-14 16:27:39,217 - INFO -   ⏰ 16:00: kline_data - 获取股票K线数据
2025-08-14 16:27:39,217 - INFO -   ⏰ 16:30: stock_select - 执行选股程序
2025-08-14 16:27:39,217 - INFO -   ⏰ 17:10: thsfp_main - 执行同花顺数据处理（爬取+OCR+后处理+清理）
2025-08-14 16:27:39,217 - INFO - ✅ 调度器启动完成，等待任务执行...
2025-08-14 16:27:40,716 - ERROR - [fupan_data] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit-marketanalysis.py", line 1150, in analyze_single_date
    print(f"\U0001f50d : {selected_date.strftime('%Y-%m-%d')}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f50d' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit-marketanalysis.py", line 1980, in <module>
    main()
  File "d:\Documents\Code\stock\Fupan\daily_limit-marketanalysis.py", line 1863, in main
    result = analyze_single_date(query_date, export_excel=export_excel, show_results=True)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit-marketanalysis.py", line 1431, in analyze_single_date
    print(f"\u274c {error_msg}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-08-14 16:27:40,716 - ERROR - ❌ 任务 fupan_data 执行失败，返回码: 1
2025-08-14 16:27:40,716 - INFO - 🔄 重试执行任务 fupan_data (第1次重试)
2025-08-14 16:27:40,790 - INFO - [precise_query] 输出: ڻȡ...
2025-08-14 16:27:40,790 - ERROR - [precise_query] 错误: C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\py_mini_racer\py_mini_racer.py:13: UserWarning: pkg_resources is deprecated as an API. See https://setuptools.pypa.io/en/latest/pkg_resources.html. The pkg_resources package is slated for removal as early as 2025-11-30. Refrain from using this package or pin to Setuptools<81.
  import pkg_resources
Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1043, in main
    query_dates = parse_dates_from_args(args)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 849, in parse_dates_from_args
    print(f"\U0001f4c5 ʹõ: {query_date.strftime('%Y%m%d')}")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f4c5' in position 0: illegal multibyte sequence

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1123, in <module>
    main()
  File "d:\Documents\Code\stock\Fupan\daily_limit_precisequery.py", line 1106, in main
    print(f"\u274c ִʧ: {str(e)}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u274c' in position 0: illegal multibyte sequence
2025-08-14 16:27:40,790 - ERROR - ❌ 任务 precise_query 执行失败，返回码: 1
2025-08-14 16:27:40,791 - INFO - 🔄 重试执行任务 precise_query (第1次重试)
2025-08-14 16:28:39,213 - INFO - 🚀 开始执行任务: env_update - 更新stock_env环境的所有pip包
2025-08-14 16:28:39,213 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\env\3_update_packages.py --all --no-backup
2025-08-14 16:28:39,213 - INFO - 工作目录: d:\Documents\Code\stock\env
2025-08-14 16:28:41,089 - ERROR - [env_update] 错误: Traceback (most recent call last):
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 390, in <module>
    main()
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 355, in main
    current_env = check_stock_env()
                  ^^^^^^^^^^^^^^^^^
  File "d:\Documents\Code\stock\env\3_update_packages.py", line 51, in check_stock_env
    print(f"\u2713 ǰ: {current_env}")
UnicodeEncodeError: 'gbk' codec can't encode character '\u2713' in position 0: illegal multibyte sequence
2025-08-14 16:28:41,089 - ERROR - ❌ 任务 env_update 执行失败，返回码: 1
2025-08-14 16:28:41,089 - INFO - 🔄 重试执行任务 env_update (第1次重试)
2025-08-14 16:30:00,264 - INFO - 🚀 开始执行任务: stock_select - 执行选股程序
2025-08-14 16:30:00,265 - INFO - 执行命令: C:\Users\<USER>\.conda\envs\stock_env\python.exe d:\Documents\Code\stock\Zge\select_stock.py --data-dir stock/Zge/data --config stock/Zge/configs.json
2025-08-14 16:30:00,265 - INFO - 工作目录: d:\Documents\Code\stock\Zge
2025-08-14 16:30:06,150 - INFO - [stock_select] 输出: 2025-08-14 16:30:06,065 [INFO] δָ --dateʹ 2025-08-14
2025-08-14 16:30:06,065 [ERROR] ļ stock\Zge\configs.json
2025-08-14 16:30:06,151 - ERROR - ❌ 任务 stock_select 执行失败，返回码: 1
2025-08-14 16:30:06,151 - INFO - 🔄 重试执行任务 stock_select (第1次重试)
