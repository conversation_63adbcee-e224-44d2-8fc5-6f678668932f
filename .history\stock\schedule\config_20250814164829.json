{"tasks": [{"name": "env_update", "description": "更新stock_env环境的所有pip包", "script_path": "stock/env", "script_file": "3_update_packages.py", "args": ["--all", "--no-backup"], "schedule_time": "startup+1min", "enabled": true, "timeout": 1800}, {"name": "precise_query", "description": "获取当日涨停精确查询数据", "script_path": "stock/Fupan", "script_file": "daily_limit_precisequery.py", "args": ["--single"], "schedule_time": "15:05", "enabled": true, "timeout": 1800}, {"name": "fupan_data", "description": "获取当天复盘数据（涨停精确查询和市场分析）", "script_path": "stock/Fupan", "script_file": "daily_limit-marketanalysis.py", "args": ["--range --limit 8"], "schedule_time": "15:10", "enabled": true, "timeout": 1800}, {"name": "kline_data", "description": "获取股票K线数据", "script_path": "stock/Zge", "script_file": "fetch_kline.py", "args": ["--datasource", "tushare", "--frequency", "4", "--exclude-gem", "--min-mktcap", "5e9", "--max-mktcap", "+inf", "--start", "20190101", "--end", "today", "--out", "stock/Zge/data", "--workers", "8"], "schedule_time": "16:00", "enabled": true, "timeout": 3600}, {"name": "stock_select", "description": "执行选股程序", "script_path": "stock/Zge", "script_file": "select_stock.py", "args": ["--data-dir", "stock/Zge/data", "--config", "stock/Zge/configs.json"], "schedule_time": "16:30", "enabled": true, "timeout": 1800}, {"name": "thsfp_main", "description": "执行同花顺数据处理（爬取+OCR+后处理+清理）", "script_path": "stock/thsfp", "script_file": "main_controller.py", "args": ["full"], "schedule_time": "17:10", "enabled": true, "timeout": 2400}], "settings": {"log_file": "scheduler.log", "weekend_skip": true, "max_retry": 3, "retry_delay_minutes": 10, "project_root": "d:/Documents/Code"}}